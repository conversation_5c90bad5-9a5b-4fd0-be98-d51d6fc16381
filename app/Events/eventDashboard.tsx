import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { Ionicons, MaterialIcons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LineChart } from 'react-native-chart-kit';

import { useColorScheme } from '~/lib/useColorScheme';
import { Button, ButtonText } from '~/components/ui/button';
import { useEventStore } from '~/store/store';
import events from '~/data/events.json';

export default function EventDashboardScreen() {
  const router = useRouter();
  const { colors, isDark } = useColorScheme();
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const eventId = params.eventId ? Number(params.eventId) : null;
  
  const [event, setEvent] = useState(null);
  const [ticketsSold, setTicketsSold] = useState(0);
  const [revenue, setRevenue] = useState(0);
  const [attendees, setAttendees] = useState(0);
  
  // Mock data for charts
  const salesData = {
    labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    datasets: [
      {
        data: [5, 8, 12, 15, 20, 25, 30],
        color: (opacity = 1) => isDark ? `rgba(139, 92, 246, ${opacity})` : `rgba(124, 58, 237, ${opacity})`,
        strokeWidth: 2
      }
    ],
  };

  useEffect(() => {
    if (eventId) {
      // In a real app, you would fetch event data from an API
      const foundEvent = events.find(e => e.id === eventId);
      if (foundEvent) {
        setEvent(foundEvent);
        
        // Calculate mock statistics
        let totalTickets = 0;
        let totalRevenue = 0;
        
        if (foundEvent.ticketSetup.hasLevels && foundEvent.ticketSetup.levels) {
          foundEvent.ticketSetup.levels.forEach(level => {
            // Simulate that 60% of tickets are sold
            const soldForLevel = Math.floor(level.quantity * 0.6);
            totalTickets += soldForLevel;
            totalRevenue += soldForLevel * level.price;
          });
        } else if (foundEvent.ticketSetup.totalTickets) {
          // Simulate that 60% of tickets are sold
          totalTickets = Math.floor(foundEvent.ticketSetup.totalTickets * 0.6);
          if (foundEvent.isPaid && foundEvent.ticketSetup.price) {
            totalRevenue = totalTickets * foundEvent.ticketSetup.price;
          }
        }
        
        setTicketsSold(totalTickets);
        setRevenue(totalRevenue);
        setAttendees(Math.floor(totalTickets * 0.9)); // Assume 90% of ticket holders attended
      }
    }
  }, [eventId]);

  const handlePromoteEvent = () => {
    router.push({
      pathname: '/Events/eventPromotions',
      params: { eventId }
    });
  };

  const handleCashOut = () => {
    // In a real app, this would initiate a payment process
    alert('Cash out request initiated. Funds will be transferred to your account within 3-5 business days.');
  };

  if (!event) {
    return (
      <View className="flex-1 items-center justify-center" style={{ backgroundColor: colors.background }}>
        <Text style={{ color: colors.foreground }}>Loading event data...</Text>
      </View>
    );
  }

  return (
    <View
      className="flex-1"
      style={{
        backgroundColor: colors.background,
        paddingTop: insets.top,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Header */}
      <View className="flex-row items-center justify-between px-4 py-4">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text className="font-bold text-xl" style={{ color: colors.foreground }}>
          Event Dashboard
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 40 }}>
        {/* Event Card */}
        <View className="mx-4 mb-6 overflow-hidden rounded-xl" style={{ backgroundColor: colors.grey5 }}>
          {event.coverImage ? (
            <Image
              source={{ uri: event.coverImage }}
              className="h-32 w-full"
              resizeMode="cover"
            />
          ) : (
            <View className="h-32 w-full items-center justify-center bg-violet-600/20">
              <FontAwesome5 name="calendar-alt" size={40} color={isDark ? '#d1d5db' : '#6b7280'} />
            </View>
          )}
          <View className="p-4">
            <Text className="font-bold text-xl" style={{ color: colors.foreground }}>
              {event.title}
            </Text>
            <Text className="mt-1" style={{ color: colors.grey }}>
              {new Date(event.startDateTime).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
              })}
            </Text>
          </View>
        </View>

        {/* Stats Cards */}
        <View className="mx-4 mb-6 flex-row justify-between">
          {/* Tickets Sold */}
          <View 
            className="w-[31%] rounded-xl p-3" 
            style={{ backgroundColor: colors.grey5 }}
          >
            <View className="mb-2 h-8 w-8 items-center justify-center rounded-full bg-violet-600/20">
              <FontAwesome5 name="ticket-alt" size={16} color={isDark ? '#d1d5db' : '#6b7280'} />
            </View>
            <Text className="font-bold text-xl" style={{ color: colors.foreground }}>
              {ticketsSold}
            </Text>
            <Text className="text-xs" style={{ color: colors.grey }}>
              Tickets Sold
            </Text>
          </View>
          
          {/* Revenue */}
          <View 
            className="w-[31%] rounded-xl p-3" 
            style={{ backgroundColor: colors.grey5 }}
          >
            <View className="mb-2 h-8 w-8 items-center justify-center rounded-full bg-green-600/20">
              <FontAwesome5 name="dollar-sign" size={16} color={isDark ? '#d1d5db' : '#6b7280'} />
            </View>
            <Text className="font-bold text-xl" style={{ color: colors.foreground }}>
              ${revenue}
            </Text>
            <Text className="text-xs" style={{ color: colors.grey }}>
              Revenue
            </Text>
          </View>
          
          {/* Attendees */}
          <View 
            className="w-[31%] rounded-xl p-3" 
            style={{ backgroundColor: colors.grey5 }}
          >
            <View className="mb-2 h-8 w-8 items-center justify-center rounded-full bg-blue-600/20">
              <Ionicons name="people" size={16} color={isDark ? '#d1d5db' : '#6b7280'} />
            </View>
            <Text className="font-bold text-xl" style={{ color: colors.foreground }}>
              {attendees}
            </Text>
            <Text className="text-xs" style={{ color: colors.grey }}>
              Attendees
            </Text>
          </View>
        </View>

        {/* Sales Chart */}
        <View className="mx-4 mb-6 rounded-xl p-4" style={{ backgroundColor: colors.grey5 }}>
          <Text className="mb-4 font-bold text-lg" style={{ color: colors.foreground }}>
            Ticket Sales
          </Text>
          <LineChart
            data={salesData}
            width={320}
            height={180}
            chartConfig={{
              backgroundColor: 'transparent',
              backgroundGradientFrom: 'transparent',
              backgroundGradientTo: 'transparent',
              decimalPlaces: 0,
              color: (opacity = 1) => isDark ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
              labelColor: (opacity = 1) => isDark ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
              style: {
                borderRadius: 16,
              },
              propsForDots: {
                r: '6',
                strokeWidth: '2',
                stroke: isDark ? '#8b5cf6' : '#7c3aed',
              },
            }}
            bezier
            style={{
              marginVertical: 8,
              borderRadius: 16,
            }}
          />
        </View>

        {/* Action Buttons */}
        <View className="mx-4 mb-6 space-y-4">
          {/* Promote Event Button */}
          <TouchableOpacity
            className="flex-row items-center rounded-xl p-4"
            style={{ backgroundColor: colors.grey5 }}
            onPress={handlePromoteEvent}>
            <View className="mr-4 h-10 w-10 items-center justify-center rounded-full bg-violet-600/20">
              <MaterialIcons name="campaign" size={20} color={isDark ? '#d1d5db' : '#6b7280'} />
            </View>
            <View className="flex-1">
              <Text className="font-bold text-base" style={{ color: colors.foreground }}>
                Promote Event
              </Text>
              <Text className="text-sm" style={{ color: colors.grey }}>
                Add gallery, videos, and stories
              </Text>
            </View>
            <MaterialIcons name="chevron-right" size={24} color={isDark ? '#d1d5db' : '#6b7280'} />
          </TouchableOpacity>

          {/* Cash Out Button */}
          {revenue > 0 && (
            <Button
              size="lg"
              variant="solid"
              className={`h-14 rounded-xl ${isDark ? 'bg-green-700' : 'bg-green-600'}`}
              onPress={handleCashOut}>
              <FontAwesome5 name="money-bill-wave" size={16} color="#fff" className="mr-2" />
              <Text className="font-semibold text-white">Cash Out ${revenue}</Text>
            </Button>
          )}
        </View>

        {/* Ticket Breakdown */}
        <View className="mx-4 mb-6 rounded-xl p-4" style={{ backgroundColor: colors.grey5 }}>
          <Text className="mb-4 font-bold text-lg" style={{ color: colors.foreground }}>
            Ticket Breakdown
          </Text>
          
          {event.ticketSetup.hasLevels && event.ticketSetup.levels ? (
            event.ticketSetup.levels.map((level, index) => {
              const soldForLevel = Math.floor(level.quantity * 0.6); // Simulate 60% sold
              return (
                <View key={index} className="mb-3 flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <View 
                      className="mr-3 h-8 w-8 items-center justify-center rounded-full"
                      style={{ 
                        backgroundColor: level.type === 'VIP' 
                          ? '#f59e0b20' 
                          : level.type === 'Premium' 
                            ? '#3b82f620' 
                            : '#8b5cf620' 
                      }}
                    >
                      <FontAwesome5 
                        name="ticket-alt" 
                        size={14} 
                        color={isDark ? '#d1d5db' : '#6b7280'} 
                      />
                    </View>
                    <Text className="font-medium" style={{ color: colors.foreground }}>
                      {level.type}
                    </Text>
                  </View>
                  <View className="flex-row items-center">
                    <Text style={{ color: colors.foreground }}>
                      {soldForLevel}/{level.quantity} sold
                    </Text>
                    <Text className="ml-4 font-bold" style={{ color: colors.foreground }}>
                      ${soldForLevel * level.price}
                    </Text>
                  </View>
                </View>
              );
            })
          ) : (
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className="mr-3 h-8 w-8 items-center justify-center rounded-full bg-violet-600/20">
                  <FontAwesome5 name="ticket-alt" size={14} color={isDark ? '#d1d5db' : '#6b7280'} />
                </View>
                <Text className="font-medium" style={{ color: colors.foreground }}>
                  Standard
                </Text>
              </View>
              <View className="flex-row items-center">
                <Text style={{ color: colors.foreground }}>
                  {ticketsSold}/{event.ticketSetup.totalTickets} sold
                </Text>
                <Text className="ml-4 font-bold" style={{ color: colors.foreground }}>
                  ${revenue}
                </Text>
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}
