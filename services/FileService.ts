import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class FileService {
  static async uploadImage(imageFile: File, email: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();
      formData.append('imageFile', imageFile);
      formData.append('email', email);

      const response = await axiosInstance.post('/file/v1/upload-image', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      return data;
    } catch (error) {
      throw error;
    }
  }

  static async uploadManyImages(imageFiles: File[], id: string, type: string) {
    try {
      console.log(imageFiles);
      console.log(id, type);
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();
      imageFiles.forEach((file) => formData.append('imageFiles', file));
      formData.append('uploadType', type);
      formData.append('entityId', id);

      const response = await axiosInstance.post('/file/v1/upload-images', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }
}
